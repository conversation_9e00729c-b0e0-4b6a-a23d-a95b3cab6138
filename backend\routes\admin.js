const express = require('express');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Evidence = require('../models/Evidence');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const blockchainService = require('../utils/blockchain');
const archiver = require('archiver');
const fs = require('fs');
const path = require('path');



const router = express.Router();

// Test endpoint - temporarily without auth for debugging
router.get('/test', (req, res) => {
  console.log('🧪 Admin test endpoint called');
  res.json({ success: true, message: 'Admin routes working' });
});

// @route   GET /api/admin/users
// @desc    Get all users (admin only)
// @access  Private (Admin)
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    
    // Build search query
    const query = { isActive: true };
    
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { fullName: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    // Get users with pagination
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await User.countDocuments(query);
    
    res.json({
      success: true,
      users,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users
// @desc    Create new user (admin only)
// @access  Private (Admin)
router.post('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { username, email, password, fullName, role, department, phone, badgeNumber, notes, isActive } = req.body;
    
    // Validation
    if (!username || !email || !password || !fullName || !role) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this username or email already exists'
      });
    }
    
    // Create new user
    const user = new User({
      username,
      email,
      password,
      fullName,
      role,
      department: department || 'General',
      phone,
      badgeNumber,
      notes,
      isActive: isActive !== undefined ? isActive : true,
      createdBy: req.user._id
    });
    
    await user.save();
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/users/:id
// @desc    Get single user (admin only)
// @access  Private (Admin)
router.get('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/users/:id
// @desc    Update user (admin only)
// @access  Private (Admin)
router.put('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, fullName, role, department, phone, badgeNumber, notes, isActive } = req.body;
    
    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Prevent admin from deactivating themselves
    if (user._id.toString() === req.user._id.toString() && isActive === false) {
      return res.status(400).json({
        success: false,
        message: 'You cannot deactivate your own account'
      });
    }
    
    // Update user fields
    if (username) user.username = username;
    if (email) user.email = email;
    if (fullName) user.fullName = fullName;
    if (role) user.role = role;
    if (department) user.department = department;
    if (phone !== undefined) user.phone = phone;
    if (badgeNumber !== undefined) user.badgeNumber = badgeNumber;
    if (notes !== undefined) user.notes = notes;
    if (typeof isActive === 'boolean') user.isActive = isActive;
    
    user.updatedAt = new Date();
    user.updatedBy = req.user._id;
    
    await user.save();
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    res.json({
      success: true,
      message: 'User updated successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/admin/users/:id
// @desc    Delete user (admin only)
// @access  Private (Admin)
router.delete('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Prevent admin from deleting themselves
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete your own account'
      });
    }
    
    // Soft delete - deactivate instead of removing
    user.isActive = false;
    user.deletedAt = new Date();
    user.deletedBy = req.user._id;
    
    await user.save();
    
    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users/:id/reset-password
// @desc    Reset user password (admin only)
// @access  Private (Admin)
router.post('/users/:id/reset-password', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Generate a temporary password
    const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);

    // Update password
    user.password = tempPassword;
    user.updatedAt = new Date();
    user.updatedBy = req.user._id;
    user.passwordResetRequired = true; // Flag to force password change on next login

    await user.save();

    res.json({
      success: true,
      message: 'Password reset successfully',
      newPassword: tempPassword
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users/:id/toggle-status
// @desc    Toggle user active status (admin only)
// @access  Private (Admin)
router.post('/users/:id/toggle-status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from deactivating themselves
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot deactivate your own account'
      });
    }

    // Toggle status
    user.isActive = !user.isActive;
    user.updatedAt = new Date();
    user.updatedBy = req.user._id;

    await user.save();

    res.json({
      success: true,
      message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`,
      user: {
        _id: user._id,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/user-stats
// @desc    Get user statistics (admin only)
// @access  Temporarily public for debugging
router.get('/user-stats', async (req, res) => {
  try {
    console.log('Loading user stats...');

    // Add timeout to prevent hanging
    const timeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database query timeout')), 5000)
    );

    const statsPromise = Promise.all([
      User.countDocuments({ isActive: true }).maxTimeMS(3000),
      User.countDocuments({ role: 'admin', isActive: true }).maxTimeMS(3000),
      User.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        isActive: true
      }).maxTimeMS(3000)
    ]);

    const [totalUsers, adminUsers, newUsersThisMonth] = await Promise.race([statsPromise, timeout]);

    console.log('User stats loaded:', { totalUsers, adminUsers, newUsersThisMonth });

    res.json({
      success: true,
      stats: {
        totalUsers: totalUsers || 0,
        activeUsers: totalUsers || 0,
        adminUsers: adminUsers || 0,
        newUsersThisMonth: newUsersThisMonth || 0
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);

    // Return default stats if database is empty or has issues
    res.json({
      success: true,
      stats: {
        totalUsers: 0,
        activeUsers: 0,
        adminUsers: 0,
        newUsersThisMonth: 0
      }
    });
  }
});

// @route   POST /api/admin/bulk-user-action
// @desc    Perform bulk action on users (admin only)
// @access  Private (Admin)
router.post('/bulk-user-action', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { action, userIds } = req.body;

    if (!action || !userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action or user IDs'
      });
    }

    // Prevent admin from affecting their own account in bulk operations
    const currentUserId = req.user._id.toString();
    const filteredUserIds = userIds.filter(id => id !== currentUserId);

    if (filteredUserIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot perform bulk actions on your own account'
      });
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'activate':
        updateData = { isActive: true, updatedAt: new Date(), updatedBy: req.user._id };
        message = `${filteredUserIds.length} user(s) activated successfully`;
        break;
      case 'deactivate':
        updateData = { isActive: false, updatedAt: new Date(), updatedBy: req.user._id };
        message = `${filteredUserIds.length} user(s) deactivated successfully`;
        break;
      case 'delete':
        updateData = { isActive: false, deletedAt: new Date(), deletedBy: req.user._id };
        message = `${filteredUserIds.length} user(s) deleted successfully`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    await User.updateMany(
      { _id: { $in: filteredUserIds } },
      updateData
    );

    res.json({
      success: true,
      message
    });
  } catch (error) {
    console.error('Bulk user action error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/stats
// @desc    Get system statistics (admin only)
// @access  Temporarily public for debugging
router.get('/stats', async (req, res) => {
  try {
    // Get user statistics
    const totalUsers = await User.countDocuments({ isActive: true });
    const usersByRole = await User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // Get evidence statistics
    const totalEvidence = await Evidence.countDocuments();
    const verifiedEvidence = await Evidence.countDocuments({ isVerified: true });

    const evidenceByMonth = await Evidence.aggregate([
      {
        $addFields: {
          dateToUse: {
            $ifNull: ['$uploadedAt', '$createdAt']
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$dateToUse' },
            month: { $month: '$dateToUse' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);

    // Recent activity
    const recentUsers = await User.find({ isActive: true })
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(5);

    const recentEvidence = await Evidence.find()
      .populate('uploader', 'username firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5);

    const statsData = {
      users: {
        total: totalUsers,
        byRole: usersByRole,
        recent: recentUsers
      },
      evidence: {
        total: totalEvidence,
        verified: verifiedEvidence,
        verificationRate: totalEvidence > 0 ? ((verifiedEvidence / totalEvidence) * 100).toFixed(1) : 0,
        byMonth: evidenceByMonth,
        recent: recentEvidence
      }
    };

    res.json({
      success: true,
      stats: statsData
    });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/legal-export/:caseId
// @desc    Generate legal export bundle with blockchain proof
// @access  Private (Admin only)
router.get('/legal-export/:caseId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { caseId } = req.params;

    // Get all evidence for the case
    const evidences = await Evidence.find({
      caseId: caseId,
      isActive: true
    })
      .populate('uploader', 'username firstName lastName role')
      .populate('accessLog.accessor', 'username firstName lastName role')
      .populate('verificationAttempts.verifier', 'username firstName lastName role');

    if (evidences.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No evidence found for this case'
      });
    }

    // Create ZIP archive
    const archive = archiver('zip', { zlib: { level: 9 } });

    res.attachment(`case-${caseId}-legal-export.zip`);
    archive.pipe(res);

    // Add evidence files and metadata
    for (const evidence of evidences) {
      // Add the actual file
      if (fs.existsSync(evidence.filePath)) {
        archive.file(evidence.filePath, { name: `evidence/${evidence.originalName}` });
      }

      // Create metadata file
      const metadata = {
        evidenceId: evidence.evidenceId,
        fileName: evidence.originalName,
        fileHash: evidence.fileHash,
        uploadedAt: evidence.uploadedAt,
        uploader: evidence.uploader,
        description: evidence.description,
        caseId: evidence.caseId,
        blockchainTxHash: evidence.blockchainTxHash,
        blockNumber: evidence.blockNumber,
        isVerified: evidence.isVerified,
        verificationAttempts: evidence.verificationAttempts,
        accessLog: evidence.accessLog,
        tags: evidence.tags
      };

      archive.append(JSON.stringify(metadata, null, 2), {
        name: `metadata/${evidence.evidenceId}-metadata.json`
      });

      // Create chain of custody report
      const custodyReport = generateChainOfCustodyReport(evidence);
      archive.append(custodyReport, {
        name: `custody/${evidence.evidenceId}-custody.txt`
      });
    }

    // Add case summary
    const caseSummary = generateCaseSummary(caseId, evidences);
    archive.append(caseSummary, { name: 'case-summary.txt' });

    archive.finalize();

  } catch (error) {
    console.error('Legal export error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating legal export'
    });
  }
});

// @route   GET /api/admin/blockchain-health
// @desc    Get blockchain health monitor data
// @access  Temporarily public for debugging
router.get('/blockchain-health', async (req, res) => {
  try {
    const healthData = await getBlockchainHealthData();
    res.json({
      success: true,
      health: healthData
    });
  } catch (error) {
    console.error('Blockchain health error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blockchain health data'
    });
  }
});

// @route   GET /api/admin/real-time-logs
// @desc    Get real-time system logs and timestamps
// @access  Temporarily public for debugging
router.get('/real-time-logs', async (req, res) => {
  try {
    const logs = await getRealTimeLogs();
    res.json({
      success: true,
      logs: logs
    });
  } catch (error) {
    console.error('Real-time logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching real-time logs'
    });
  }
});

// @route   POST /api/admin/create-sample-evidence
// @desc    Create sample evidence for testing (Admin only)
// @access  Private (Admin only)
router.post('/create-sample-evidence', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const now = new Date();
    const sampleEvidence = [
      {
        evidenceId: Date.now(),
        fileName: 'sample-document.pdf',
        originalName: 'sample-document.pdf',
        fileHash: 'sha256-' + Math.random().toString(36).substring(2, 15),
        filePath: '/uploads/sample-document.pdf',
        fileSize: 1024000,
        mimeType: 'application/pdf',
        description: 'Sample evidence document for testing',
        caseId: 'CASE-001',
        uploader: req.user._id,
        uploaderAddress: null,
        blockchainTxHash: null,
        blockNumber: null,
        gasUsed: null,
        tags: ['sample', 'test'],
        isVerified: false,
        isActive: true,
        uploadedAt: now
      },
      {
        evidenceId: Date.now() + 1,
        fileName: 'sample-image.jpg',
        originalName: 'sample-image.jpg',
        fileHash: 'sha256-' + Math.random().toString(36).substring(2, 15),
        filePath: '/uploads/sample-image.jpg',
        fileSize: 2048000,
        mimeType: 'image/jpeg',
        description: 'Sample evidence image for testing',
        caseId: 'CASE-002',
        uploader: req.user._id,
        uploaderAddress: null,
        blockchainTxHash: null,
        blockNumber: null,
        gasUsed: null,
        tags: ['sample', 'photo'],
        isVerified: true,
        isActive: true,
        uploadedAt: new Date(now.getTime() - 60000) // 1 minute ago
      },
      {
        evidenceId: Date.now() + 2,
        fileName: 'sample-video.mp4',
        originalName: 'sample-video.mp4',
        fileHash: 'sha256-' + Math.random().toString(36).substring(2, 15),
        filePath: '/uploads/sample-video.mp4',
        fileSize: 5120000,
        mimeType: 'video/mp4',
        description: 'Sample evidence video for testing',
        caseId: 'CASE-003',
        uploader: req.user._id,
        uploaderAddress: null,
        blockchainTxHash: null,
        blockNumber: null,
        gasUsed: null,
        tags: ['sample', 'video'],
        isVerified: false,
        isActive: true,
        uploadedAt: new Date(now.getTime() - 120000) // 2 minutes ago
      }
    ];

    const createdEvidence = await Evidence.insertMany(sampleEvidence);

    res.json({
      success: true,
      message: 'Sample evidence created successfully',
      evidence: createdEvidence
    });
  } catch (error) {
    console.error('Create sample evidence error:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating sample evidence'
    });
  }
});

// Helper function to generate chain of custody report
function generateChainOfCustodyReport(evidence) {
  let report = `CHAIN OF CUSTODY REPORT\n`;
  report += `========================\n\n`;
  report += `Evidence ID: ${evidence.evidenceId}\n`;
  report += `File Name: ${evidence.originalName}\n`;
  report += `File Hash: ${evidence.fileHash}\n`;
  report += `Case ID: ${evidence.caseId}\n`;
  report += `Upload Date: ${evidence.uploadedAt}\n`;
  report += `Uploader: ${evidence.uploader?.firstName} ${evidence.uploader?.lastName} (${evidence.uploader?.username})\n`;
  report += `Blockchain TX: ${evidence.blockchainTxHash || 'N/A'}\n`;
  report += `Block Number: ${evidence.blockNumber || 'N/A'}\n\n`;

  report += `ACCESS LOG\n`;
  report += `----------\n`;
  if (evidence.accessLog && evidence.accessLog.length > 0) {
    evidence.accessLog.forEach(log => {
      report += `${log.timestamp} - ${log.accessor?.username || 'Unknown'} - ${log.action} - IP: ${log.ipAddress}\n`;
    });
  } else {
    report += `No access log entries\n`;
  }

  report += `\nVERIFICATION HISTORY\n`;
  report += `-------------------\n`;
  if (evidence.verificationAttempts && evidence.verificationAttempts.length > 0) {
    evidence.verificationAttempts.forEach(attempt => {
      report += `${attempt.timestamp} - ${attempt.verifier?.username || 'Unknown'} - ${attempt.result ? 'PASSED' : 'FAILED'}\n`;
    });
  } else {
    report += `No verification attempts\n`;
  }

  return report;
}

// Helper function to generate case summary
function generateCaseSummary(caseId, evidences) {
  let summary = `CASE SUMMARY REPORT\n`;
  summary += `==================\n\n`;
  summary += `Case ID: ${caseId}\n`;
  summary += `Total Evidence Items: ${evidences.length}\n`;
  summary += `Verified Items: ${evidences.filter(e => e.isVerified).length}\n`;
  summary += `Unverified Items: ${evidences.filter(e => !e.isVerified).length}\n`;
  summary += `Export Generated: ${new Date().toISOString()}\n\n`;

  summary += `EVIDENCE INVENTORY\n`;
  summary += `-----------------\n`;
  evidences.forEach(evidence => {
    summary += `${evidence.evidenceId} - ${evidence.originalName} - ${evidence.isVerified ? 'VERIFIED' : 'UNVERIFIED'}\n`;
  });

  return summary;
}

// Helper function to get blockchain health data
async function getBlockchainHealthData() {
  try {
    const isConnected = blockchainService.isConnected();

    if (!isConnected) {
      return {
        status: 'Disconnected',
        contractStatus: 'Inactive',
        avgGasUsed: 0,
        eventsEmitted: 0,
        rolesAssigned: { uploader: 0, admin: 0 },
        network: 'Unknown'
      };
    }

    // Get blockchain stats
    const accounts = blockchainService.getAccounts();
    const totalEvidences = await blockchainService.getTotalEvidences();

    // Get recent evidence for gas calculation
    const recentEvidences = await Evidence.find({
      gasUsed: { $exists: true, $ne: null }
    }).limit(10);

    const avgGasUsed = recentEvidences.length > 0
      ? Math.round(recentEvidences.reduce((sum, e) => sum + (e.gasUsed || 0), 0) / recentEvidences.length)
      : 0;

    // Count roles
    const uploaderCount = await User.countDocuments({ role: 'Police' });
    const adminCount = await User.countDocuments({ role: 'Admin' });

    return {
      status: 'Connected',
      contractStatus: 'Active',
      avgGasUsed: avgGasUsed,
      eventsEmitted: totalEvidences.success ? totalEvidences.total : 0,
      rolesAssigned: {
        uploader: uploaderCount,
        admin: adminCount
      },
      network: process.env.BLOCKCHAIN_NETWORK || 'development',
      accounts: accounts.length,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Blockchain health data error:', error);
    return {
      status: 'Error',
      contractStatus: 'Unknown',
      avgGasUsed: 0,
      eventsEmitted: 0,
      rolesAssigned: { uploader: 0, admin: 0 },
      network: 'Unknown',
      error: error.message
    };
  }
}

// Helper function to get real-time logs
async function getRealTimeLogs() {
  try {
    // Get recent access logs from evidence
    const recentAccess = await Evidence.aggregate([
      { $unwind: '$accessLog' },
      { $sort: { 'accessLog.timestamp': -1 } },
      { $limit: 50 },
      {
        $lookup: {
          from: 'users',
          localField: 'accessLog.accessor',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $project: {
          timestamp: '$accessLog.timestamp',
          action: '$accessLog.action',
          evidenceId: '$evidenceId',
          user: { $arrayElemAt: ['$user.username', 0] },
          ipAddress: '$accessLog.ipAddress'
        }
      }
    ]);

    // Get recent verification attempts
    const recentVerifications = await Evidence.aggregate([
      { $unwind: '$verificationAttempts' },
      { $sort: { 'verificationAttempts.timestamp': -1 } },
      { $limit: 20 },
      {
        $lookup: {
          from: 'users',
          localField: 'verificationAttempts.verifier',
          foreignField: '_id',
          as: 'verifier'
        }
      },
      {
        $project: {
          timestamp: '$verificationAttempts.timestamp',
          action: 'verify',
          evidenceId: '$evidenceId',
          user: { $arrayElemAt: ['$verifier.username', 0] },
          result: '$verificationAttempts.result'
        }
      }
    ]);

    return {
      accessLogs: recentAccess,
      verificationLogs: recentVerifications,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Real-time logs error:', error);
    return {
      accessLogs: [],
      verificationLogs: [],
      error: error.message
    };
  }
}

module.exports = router;
