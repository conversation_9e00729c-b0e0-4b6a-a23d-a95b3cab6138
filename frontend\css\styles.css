/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
    overflow-x: hidden; /* Prevent horizontal scroll */
    min-height: 100vh;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: #28a745;
}

.text-danger {
    color: #dc3545;
}

.text-warning {
    color: #ffc107;
}

.text-info {
    color: #17a2b8;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    min-height: 80px;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    min-height: 60px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-brand i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    white-space: nowrap;
    font-size: 0.9rem;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-item.active {
    background-color: rgba(255,255,255,0.2);
}

.nav-item i {
    margin-right: 0.5rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-badge {
    background-color: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: bold;
}

/* Main Content */
.main-content {
    margin-top: 100px;
    min-height: calc(100vh - 100px);
    padding: 2rem;
    padding-top: 3rem;
}

.page {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.page-header h1 i {
    margin-right: 1rem;
    color: #667eea;
}

.page-header p {
    font-size: 1.1rem;
    color: #666;
}

/* Enhanced Login Page */
.login-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
    grid-column: 1 / -1;
}

.login-logo {
    margin-bottom: 1rem;
}

.login-logo i {
    font-size: 4rem;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header h1 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 700;
}

.login-subtitle {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.login-description {
    grid-column: 1;
}

.feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 10px;
    border: 1px solid #e1e5ff;
}

.feature-item i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-item h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.feature-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.login-form-container {
    grid-column: 2;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    border-radius: 10px;
    color: white;
}

.login-form-container h2 {
    color: white;
    margin-bottom: 1.5rem;
    text-align: center;
}

.login-form .form-group label {
    color: white;
    font-weight: 500;
}

.login-form .form-group input {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
}

.login-form .form-group input::placeholder {
    color: rgba(255,255,255,0.7);
}

.login-form .form-group input:focus {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.5);
}



.login-footer {
    grid-column: 1 / -1;
    margin-top: 2rem;
}

.security-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.security-notice i {
    color: #856404;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.security-notice p {
    color: #856404;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Ensure login button is clickable */
.login-form .btn {
    position: relative;
    z-index: 10;
    pointer-events: auto;
    width: 100%;
    margin-top: 1rem;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Dashboard */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1.5rem;
    color: #667eea;
}

.stat-content h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.stat-content p {
    color: #666;
    font-size: 0.9rem;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

.recent-activity {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.recent-activity h2 {
    margin-bottom: 1.5rem;
    color: #333;
}

.recent-activity h2 i {
    margin-right: 0.5rem;
    color: #667eea;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.activity-item .activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.activity-item .activity-meta {
    font-size: 0.8rem;
    color: #666;
}

/* Evidence Grid */
.evidence-filters {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-group input,
.filter-group select {
    flex: 1;
    max-width: 200px;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #667eea;
}

.evidence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.evidence-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.evidence-card:hover {
    transform: translateY(-5px);
}

.evidence-card-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.evidence-card-header h3 {
    margin-bottom: 0.5rem;
}

.evidence-card-header .evidence-id {
    font-size: 0.8rem;
    opacity: 0.8;
}

.evidence-card-body {
    padding: 1.5rem;
}

.evidence-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.evidence-meta-item {
    font-size: 0.9rem;
}

.evidence-meta-item strong {
    display: block;
    color: #333;
    margin-bottom: 0.25rem;
}

.evidence-description {
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.evidence-actions {
    display: flex;
    gap: 0.5rem;
}

.evidence-actions .btn {
    flex: 1;
    justify-content: center;
    padding: 0.5rem;
    font-size: 0.8rem;
}

/* Verification Status */
.verification-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.verification-status.verified {
    background-color: #d4edda;
    color: #155724;
}

.verification-status.unverified {
    background-color: #f8d7da;
    color: #721c24;
}

.verification-status i {
    margin-right: 0.25rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 2000;
    display: none;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 10px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1.5rem;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    z-index: 3000;
}

.loading-overlay.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.2rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: 2rem;
    z-index: 2500;
    max-width: 400px;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
    min-width: 320px;
    border-left: 4px solid #667eea;
    animation: slideIn 0.4s ease;
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.toast:hover {
    transform: translateX(-5px);
}

.toast.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
}

.toast.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #ffffff 0%, #fff8f8 100%);
}

.toast.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
}

.toast.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #ffffff 0%, #f8feff 100%);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-content .toast-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.toast-content .toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-content .toast-details {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.25rem;
}

.toast-close {
    position: absolute;
    top: 0.5rem;
    right: 0.75rem;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #999;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Enhanced Responsive Design */

/* Container max-width for better scaling */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Flexible images */
img {
    max-width: 100%;
    height: auto;
}

/* Responsive tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

table {
    min-width: 600px;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 480px) {
    .main-content {
        margin-top: 100px;
        padding: 0.5rem;
    }

    .login-container {
        margin: 0.5rem;
        padding: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .btn {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .nav-brand span {
        font-size: 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 0;
        min-height: 70px;
    }

    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        flex-direction: row;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-item {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .main-content {
        margin-top: 120px;
        padding: 1rem;
    }

    /* Admin Panel Mobile */
    .admin-sidebar {
        width: 100%;
        position: fixed;
        top: 70px;
        left: 0;
        height: calc(100vh - 70px);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1002;
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }

    .admin-main-content {
        margin-left: 0;
        padding: 1rem;
        width: 100%;
    }

    .admin-mobile-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }

    .admin-mobile-toggle {
        background: #667eea;
        color: white;
        border: none;
        padding: 0.75rem;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1.2rem;
    }

    .admin-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1001;
    }

    .admin-overlay.show {
        display: block;
    }

    .users-stats {
        grid-template-columns: 1fr;
    }

    .users-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .users-table-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .form-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .evidence-grid {
        grid-template-columns: 1fr;
    }

    .login-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin: 1rem;
        padding: 1.5rem;
    }

    .login-description {
        grid-column: 1;
    }

    .login-form-container {
        grid-column: 1;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .hero-section {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .story-content {
        grid-template-columns: 1fr;
    }

    .contact-info-inline {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-bottom-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }

    .footer-legal {
        text-align: center;
    }

    .compliance-badges {
        justify-content: center;
    }
}

/* Tablet styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-content {
        padding: 2rem;
    }

    .login-container {
        max-width: 800px;
        margin: 2rem auto;
    }

    .evidence-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop styles */
@media (min-width: 1025px) {
    .container {
        max-width: 1200px;
    }

    .main-content {
        padding: 2rem 3rem;
    }

    .login-container {
        max-width: 1000px;
        margin: 3rem auto;
    }
}

/* Large desktop styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }

    .main-content {
        padding: 3rem 4rem;
    }
}

/* Admin Panel Styles */
.admin-layout {
    display: flex;
    min-height: calc(100vh - 80px);
    background: #f8f9fa;
}

.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    left: 0;
    top: 80px;
    height: calc(100vh - 80px);
    overflow-y: auto;
    z-index: 1001;
    box-shadow: 2px 0 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    border-right: 1px solid rgba(255,255,255,0.1);
}

.admin-sidebar.pinned {
    position: fixed !important;
    z-index: 1001 !important;
    transform: translateX(0) !important;
}

/* Pin button for admin sidebar */
.admin-sidebar-pin {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s ease;
}

.admin-sidebar-pin:hover {
    background: rgba(255,255,255,0.3);
}

.admin-sidebar-pin.pinned {
    background: rgba(255,255,255,0.4);
}

.admin-sidebar-header {
    padding: 2rem 1.5rem 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.admin-sidebar-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.admin-sidebar-header p {
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

.admin-nav {
    padding: 1rem 0;
}

.admin-nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.admin-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.admin-nav-item:hover::before {
    left: 100%;
}

.admin-nav-item:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.admin-nav-item.active {
    background: rgba(255,255,255,0.2);
    border-left-color: #fff;
    box-shadow: 0 2px 15px rgba(0,0,0,0.3);
}

.admin-nav-item.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: white;
    border-radius: 2px 0 0 2px;
}

.admin-nav-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.admin-nav-item span {
    font-weight: 500;
}

.admin-main-content {
    margin-left: 280px;
    flex: 1;
    padding: 2rem;
    background: #f8f9fa;
    min-height: calc(100vh - 80px);
}

.admin-content-header {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.admin-content-header h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.8rem;
}

.admin-content-header p {
    margin: 0;
    color: #7f8c8d;
    font-size: 1rem;
}

.tab-content {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: fadeIn 0.5s ease;
}

.tab-content.active {
    display: block;
    opacity: 1;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* User Management */
.users-header {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.users-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.users-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:active {
    transform: translateY(-2px) scale(1.01);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.stat-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.stat-card:hover .stat-info h4 {
    color: #1a252f;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.stat-card:hover .stat-info p {
    color: #6c757d;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.users-table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
    margin-bottom: 2rem;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: middle;
}

.users-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
}

.users-table tbody tr {
    transition: all 0.3s ease;
    position: relative;
}

.users-table tbody tr:hover {
    background: linear-gradient(90deg, #f8f9fa, #e9ecef, #f8f9fa);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1;
}

.users-table tbody tr:hover td {
    border-color: #dee2e6;
}

.users-table-footer {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bulk-actions select {
    min-width: 150px;
}

.user-info-cell {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-details h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    color: #2c3e50;
}

.user-details p {
    margin: 0;
    font-size: 0.85rem;
    color: #7f8c8d;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.action-btn:hover::before {
    width: 100px;
    height: 100px;
}

.action-btn.edit {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.action-btn.delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.action-btn.reset {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.action-btn.toggle {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
}

.action-btn:active {
    transform: translateY(0) scale(1.05);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-name {
    font-weight: 600;
    color: #2c3e50;
}

.user-username {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.user-email {
    color: #95a5a6;
    font-size: 0.85rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: 1px solid #ddd;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: #f8f9fa;
}

.btn-icon.danger:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-icon.success:hover {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    margin: 0;
    color: #2c3e50;
}

.stat-info p {
    margin: 0;
    color: #7f8c8d;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.role-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.role-count {
    font-weight: 600;
    color: #2c3e50;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item i {
    color: #3498db;
}

.activity-item small {
    margin-left: auto;
    color: #7f8c8d;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-content.large-modal {
    max-width: 800px;
}

.modal-content.small-modal {
    max-width: 400px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #ecf0f1;
    background: #f8f9fa;
}

.modal-header h2,
.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #e74c3c;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.form-section {
    padding: 1.5rem 2rem;
    border-top: 1px solid #ecf0f1;
    background: #f8f9fa;
}

.form-section h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.form-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem 2rem;
    border-top: 1px solid #ecf0f1;
    background: #f8f9fa;
}

/* Settings */
.settings-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: #2c3e50;
}

.setting-item input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
}

.pagination-buttons {
    display: flex;
    gap: 0.5rem;
}

.pagination-buttons button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
}

.pagination-buttons button:hover {
    background: #f8f9fa;
}

.pagination-buttons button.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Home Page */
.home-container {
    max-width: 1200px;
    margin: 0 auto;
}

.hero-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-bottom: 4rem;
    padding: 3rem 0;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.hero-image {
    text-align: center;
}

.hero-image i {
    font-size: 12rem;
    color: #667eea;
    opacity: 0.1;
}

.features-section {
    margin-bottom: 4rem;
}

.features-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 3rem;
    color: #667eea;
}

.feature-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem;
    border-radius: 15px;
    color: white;
    text-align: center;
    margin-bottom: 4rem;
}

.stats-section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* About Us Page */
.about-container {
    max-width: 1200px;
    margin: 0 auto;
}

.about-header {
    text-align: center;
    margin-bottom: 4rem;
}

.about-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.about-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.about-content {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.mission-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.mission-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.mission-card:hover {
    transform: translateY(-5px);
}

.mission-icon {
    margin-bottom: 1.5rem;
}

.mission-icon i {
    font-size: 3rem;
    color: #667eea;
}

.mission-card h2 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.mission-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

.story-section {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 3rem;
    border-radius: 15px;
    border: 1px solid #e1e5ff;
}

.story-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.story-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.story-text p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.story-stats {
    display: grid;
    gap: 1.5rem;
}

.stat-highlight {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-text {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.team-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.reason-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.reason-card:hover {
    transform: translateY(-3px);
}

.reason-card i {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.reason-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.reason-card p {
    color: #666;
    line-height: 1.5;
    font-size: 0.95rem;
}

.contact-cta {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem;
    border-radius: 15px;
    text-align: center;
    color: white;
}

.contact-cta h2 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.contact-cta p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.cta-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.contact-info-inline {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-info-inline p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255,255,255,0.9);
    margin: 0;
    font-size: 0.95rem;
}

.contact-info-inline i {
    color: rgba(255,255,255,0.8);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin-top: 4rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.footer-logo i {
    color: #667eea;
    font-size: 1.5rem;
}

.footer-description {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
}

.social-link:hover {
    background: #667eea;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-links i {
    width: 16px;
    color: #667eea;
}

.contact-info .contact-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.contact-item i {
    color: #667eea;
    font-size: 1.2rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-credits {
    text-align: left;
}

.footer-credits p {
    color: #bdc3c7;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.footer-tech {
    text-align: center;
}

.tech-stack {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.tech-label {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.tech-items {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ecf0f1;
    font-size: 0.85rem;
}

.tech-item i {
    color: #667eea;
}

.footer-legal {
    text-align: right;
}

.legal-links {
    margin-bottom: 1rem;
}

.legal-links a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.legal-links a:hover {
    color: white;
}

.legal-links span {
    color: #7f8c8d;
    margin: 0 0.5rem;
}

.compliance-badges {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.badge {
    background: rgba(255,255,255,0.1);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #ecf0f1;
}

/* Verification styles */
.verification-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 1rem 0;
    overflow: hidden;
}

/* Unverified Evidence List */
.unverified-evidence-section {
    margin-top: 2rem;
}

.section-header h2 {
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.section-header p {
    color: #666;
    margin-bottom: 1rem;
}

.unverified-evidence-list {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.evidence-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.evidence-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #3498db;
}

.evidence-card.unverified {
    border-left: 4px solid #e74c3c;
}

.evidence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.evidence-id {
    font-size: 1.1rem;
    color: #2c3e50;
}

.evidence-id i {
    margin-right: 0.5rem;
    color: #3498db;
}

.evidence-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.evidence-status.unverified {
    background: #fee;
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.evidence-details p {
    margin: 0.5rem 0;
    color: #555;
}

.evidence-details strong {
    color: #2c3e50;
}

.evidence-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: right;
}

.no-evidence, .error {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

.error {
    color: #e74c3c;
}

.loading-spinner {
    text-align: center;
    padding: 2rem;
    color: #3498db;
}

.loading-spinner i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

/* Admin Features Styles */
.blockchain-health-section, .logs-section, .export-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.health-refresh, .logs-controls {
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1rem;
}

.blockchain-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.health-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.health-card.connected {
    border-color: #28a745;
    background: #d4edda;
}

.health-card.disconnected {
    border-color: #dc3545;
    background: #f8d7da;
}

.health-card.error {
    border-color: #ffc107;
    background: #fff3cd;
}

.health-card h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.health-metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.health-metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: #555;
}

.metric-value {
    font-weight: bold;
    color: #2c3e50;
}

.logs-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.logs-section-header {
    background: #e9ecef;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
    color: #495057;
}

.logs-list {
    max-height: 250px;
    overflow-y: auto;
}

.log-entry {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry:hover {
    background: #e9ecef;
}

.log-timestamp {
    color: #6c757d;
    font-size: 0.8rem;
}

.log-action {
    font-weight: bold;
    color: #495057;
}

.log-user {
    color: #007bff;
}

.log-evidence {
    color: #28a745;
}

.export-form {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.export-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 1.5rem;
}

.export-info h4 {
    color: #0056b3;
    margin-bottom: 1rem;
}

.export-info ul {
    list-style: none;
    padding: 0;
}

.export-info li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
}

.export-info li i {
    color: #007bff;
    width: 20px;
}

#auto-refresh-toggle.active {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.connected {
    background: #28a745;
}

.status-indicator.disconnected {
    background: #dc3545;
}

.status-indicator.error {
    background: #ffc107;
}

.log-result.success {
    color: #28a745;
    font-weight: bold;
}

.log-result.error {
    color: #dc3545;
    font-weight: bold;
}

.log-entry.error {
    color: #dc3545;
    font-style: italic;
    text-align: center;
}

/* Additional Mobile Responsive for Admin Panel */
@media (max-width: 480px) {
    .admin-mobile-header {
        display: flex !important;
    }

    .users-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .users-table-container {
        overflow-x: auto;
    }

    .users-table {
        min-width: 800px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .modal-content.large-modal {
        max-width: 95%;
    }

    .user-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        min-width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .users-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-box {
        width: 100%;
    }

    .users-table-footer {
        flex-direction: column;
        gap: 1rem;
    }
}
