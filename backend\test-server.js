const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001', 'http://localhost:5500'],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  console.log('🔍 Health check endpoint called');
  res.json({
    success: true,
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Test server is running'
  });
});

// Mock admin endpoints for testing
app.get('/api/admin/test', (req, res) => {
  console.log('🧪 Admin test endpoint called');
  res.json({ success: true, message: 'Admin routes working' });
});

app.get('/api/admin/users', (req, res) => {
  console.log('👥 Admin users endpoint called');
  res.json({
    success: true,
    users: [
      {
        _id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        isActive: true,
        createdAt: new Date().toISOString()
      }
    ],
    pagination: {
      currentPage: 1,
      totalPages: 1,
      totalUsers: 1,
      hasNext: false,
      hasPrev: false
    }
  });
});

app.get('/api/admin/user-stats', (req, res) => {
  console.log('📊 Admin user stats endpoint called');
  res.json({
    success: true,
    stats: {
      totalUsers: 1,
      activeUsers: 1,
      adminUsers: 1,
      newUsersThisMonth: 0
    }
  });
});

app.get('/api/admin/stats', (req, res) => {
  console.log('📈 Admin stats endpoint called');
  res.json({
    success: true,
    stats: {
      users: {
        total: 1,
        byRole: [{ _id: 'admin', count: 1 }],
        recent: []
      },
      evidence: {
        total: 0,
        verified: 0,
        verificationRate: 0,
        byMonth: [],
        recent: []
      }
    }
  });
});

app.get('/api/admin/blockchain-health', (req, res) => {
  console.log('⛓️ Admin blockchain health endpoint called');
  res.json({
    success: true,
    health: {
      status: 'Connected',
      contractStatus: 'Active',
      avgGasUsed: 21000,
      eventsEmitted: 0,
      rolesAssigned: {
        uploader: 0,
        admin: 1
      },
      network: 'development',
      accounts: 10,
      lastUpdated: new Date().toISOString()
    }
  });
});

app.get('/api/admin/real-time-logs', (req, res) => {
  console.log('📝 Admin real-time logs endpoint called');
  res.json({
    success: true,
    logs: {
      accessLogs: [],
      verificationLogs: []
    }
  });
});

// Start server
const PORT = process.env.PORT || 3001;

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`
🚀 Test Evidence Protection System Server
📍 Running on port ${PORT}
🌍 Environment: test
  `);

  console.log(`✅ Test server is listening on http://0.0.0.0:${PORT}`);
  console.log(`✅ Health check available at: http://localhost:${PORT}/api/health`);
});

// Add error handling for server
server.on('error', (error) => {
  console.error('Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${PORT} is already in use. Please choose a different port.`);
  }
});

module.exports = app;
