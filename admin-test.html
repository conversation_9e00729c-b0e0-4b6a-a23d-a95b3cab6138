<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Admin Panel Functionality Test</h1>
    
    <div class="test-section info">
        <h3>Test Instructions:</h3>
        <p>1. Make sure the test server is running on port 3001</p>
        <p>2. Click the test buttons below to verify admin panel functionality</p>
        <p>3. Check the browser console for any errors</p>
    </div>

    <div class="test-section">
        <h3>API Connection Tests</h3>
        <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <button onclick="testAdminEndpoint()">Test Admin Endpoint</button>
        <button onclick="testUserStats()">Test User Stats</button>
        <button onclick="testBlockchainHealth()">Test Blockchain Health</button>
        <div id="api-results" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Admin Panel UI Tests</h3>
        <button onclick="testAdminManagerInit()">Test AdminManager Initialization</button>
        <button onclick="testAdminNavigation()">Test Admin Navigation</button>
        <button onclick="testUserTable()">Test User Table Elements</button>
        <div id="ui-results" class="result"></div>
    </div>

    <script>
        // Mock API class for testing
        class TestAPI {
            constructor() {
                this.baseURL = 'http://localhost:3001/api';
            }

            async request(endpoint, method = 'GET') {
                try {
                    const response = await fetch(`${this.baseURL}${endpoint}`, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    return await response.json();
                } catch (error) {
                    throw new Error(`API request failed: ${error.message}`);
                }
            }

            async getBlockchainHealth() {
                return this.request('/admin/blockchain-health');
            }
        }

        const testAPI = new TestAPI();

        async function testHealthEndpoint() {
            const resultsDiv = document.getElementById('api-results');
            try {
                const result = await testAPI.request('/health');
                resultsDiv.innerHTML = `<strong>✅ Health Endpoint:</strong> ${JSON.stringify(result, null, 2)}`;
                resultsDiv.className = 'result success';
            } catch (error) {
                resultsDiv.innerHTML = `<strong>❌ Health Endpoint Failed:</strong> ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }

        async function testAdminEndpoint() {
            const resultsDiv = document.getElementById('api-results');
            try {
                const result = await testAPI.request('/admin/test');
                resultsDiv.innerHTML = `<strong>✅ Admin Test Endpoint:</strong> ${JSON.stringify(result, null, 2)}`;
                resultsDiv.className = 'result success';
            } catch (error) {
                resultsDiv.innerHTML = `<strong>❌ Admin Test Failed:</strong> ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }

        async function testUserStats() {
            const resultsDiv = document.getElementById('api-results');
            try {
                const result = await testAPI.request('/admin/user-stats');
                resultsDiv.innerHTML = `<strong>✅ User Stats:</strong> ${JSON.stringify(result, null, 2)}`;
                resultsDiv.className = 'result success';
            } catch (error) {
                resultsDiv.innerHTML = `<strong>❌ User Stats Failed:</strong> ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }

        async function testBlockchainHealth() {
            const resultsDiv = document.getElementById('api-results');
            try {
                const result = await testAPI.getBlockchainHealth();
                resultsDiv.innerHTML = `<strong>✅ Blockchain Health:</strong> ${JSON.stringify(result, null, 2)}`;
                resultsDiv.className = 'result success';
            } catch (error) {
                resultsDiv.innerHTML = `<strong>❌ Blockchain Health Failed:</strong> ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }

        function testAdminManagerInit() {
            const resultsDiv = document.getElementById('ui-results');
            try {
                // Test if we can create AdminManager (would need to load the actual script)
                resultsDiv.innerHTML = `<strong>ℹ️ AdminManager Test:</strong> This test requires the main application to be loaded. Please test in the main application.`;
                resultsDiv.className = 'result info';
            } catch (error) {
                resultsDiv.innerHTML = `<strong>❌ AdminManager Failed:</strong> ${error.message}`;
                resultsDiv.className = 'result error';
            }
        }

        function testAdminNavigation() {
            const resultsDiv = document.getElementById('ui-results');
            resultsDiv.innerHTML = `<strong>ℹ️ Navigation Test:</strong> Please test admin navigation in the main application by clicking Admin Panel in the navigation menu.`;
            resultsDiv.className = 'result info';
        }

        function testUserTable() {
            const resultsDiv = document.getElementById('ui-results');
            resultsDiv.innerHTML = `<strong>ℹ️ User Table Test:</strong> Please test user table functionality in the main application's admin panel.`;
            resultsDiv.className = 'result info';
        }

        // Auto-run basic connectivity test
        window.onload = function() {
            setTimeout(testHealthEndpoint, 1000);
        };
    </script>
</body>
</html>
