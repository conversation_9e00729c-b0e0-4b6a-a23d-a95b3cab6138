const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// Import database connection
const connectDB = require('../config/database');

// Import utilities
const blockchainService = require('./utils/blockchain');
const { ensureUploadDirectory } = require('./utils/fileHandler');

// Import routes
const authRoutes = require('./routes/auth');
const evidenceRoutes = require('./routes/evidence');

const app = express();

// Connect to database
connectDB();

// Security middleware - temporarily disabled for debugging
// app.use(helmet({
//   crossOriginResourcePolicy: { policy: "cross-origin" }
// }));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourdomain.com']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001', 'http://localhost:5500'],
  credentials: true
}));

// Rate limiting - temporarily disabled for debugging
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 100, // limit each IP to 100 requests per windowMs
//   message: {
//     success: false,
//     message: 'Too many requests from this IP, please try again later'
//   }
// });
// app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/evidence', evidenceRoutes);
app.use('/api/admin', require('./routes/admin'));

// Health check endpoint
app.get('/api/health', async (req, res) => {
  console.log('🔍 Health check endpoint called');
  try {
    const response = {
      success: true,
      status: 'OK',
      timestamp: new Date().toISOString(),
      message: 'Server is running'
    };

    console.log('✅ Health check response:', response);
    res.json(response);
  } catch (error) {
    console.error('❌ Health check error:', error);
    res.status(500).json({
      success: false,
      status: 'ERROR',
      message: error.message
    });
  }
});

// Blockchain status endpoint
app.get('/api/blockchain/status', async (req, res) => {
  try {
    const isConnected = blockchainService.isConnected();
    const accounts = blockchainService.getAccounts();
    
    let totalEvidences = 0;
    if (isConnected) {
      const result = await blockchainService.getTotalEvidences();
      totalEvidences = result.success ? result.total : 0;
    }

    res.json({
      success: true,
      blockchain: {
        connected: isConnected,
        accounts: accounts.length,
        totalEvidences,
        network: process.env.BLOCKCHAIN_NETWORK || 'development'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error checking blockchain status',
      error: error.message
    });
  }
});

// Serve frontend static files
app.use(express.static(path.join(__dirname, '../frontend')));

// Serve frontend for all non-API routes
app.get('*', (req, res) => {
  // Don't serve frontend for API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({
      success: false,
      message: 'API route not found'
    });
  }

  // Serve the frontend index.html for all other routes
  res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  
  // Multer errors
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: 'File size too large'
    });
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      message: 'Unexpected file field'
    });
  }

  // Default error response
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error'
  });
});

// Initialize services
const initializeServices = async () => {
  try {
    console.log('Initializing services...');
    
    // Ensure upload directory exists
    await ensureUploadDirectory();
    console.log('✓ Upload directory ready');
    
    // Initialize blockchain connection
    try {
      const blockchainInitialized = await blockchainService.initialize();
      if (blockchainInitialized) {
        console.log('✓ Blockchain service connected');
      } else {
        console.log('⚠ Blockchain service not available - running in offline mode');
      }
    } catch (error) {
      console.log('⚠ Blockchain initialization failed - running in offline mode:', error.message);
    }
    
    console.log('Services initialization completed');
  } catch (error) {
    console.error('Service initialization error:', error);
  }
};

// Start server
const PORT = process.env.PORT || 3000;

const server = app.listen(PORT, '0.0.0.0', async () => {
  console.log(`
🚀 Evidence Protection System Server
📍 Running on port ${PORT}
🌍 Environment: ${process.env.NODE_ENV || 'development'}
📊 Database: ${process.env.MONGODB_URI || 'mongodb://localhost:27017/evidence_protection'}
⛓️  Blockchain: ${process.env.BLOCKCHAIN_NETWORK || 'development'}
  `);

  console.log(`✅ Server is listening on http://0.0.0.0:${PORT}`);
  console.log(`✅ Health check available at: http://localhost:${PORT}/api/health`);

  // Initialize services after server starts
  try {
    await initializeServices();
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Service initialization failed:', error);
  }
});

// Add error handling for server
server.on('error', (error) => {
  console.error('Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${PORT} is already in use. Please choose a different port.`);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

module.exports = app;
